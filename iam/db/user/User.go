package user

import (
	"errors"
	"regexp"
	"time"
	"unicode"

	"git.gig.tech/gig-meneja/iam/db"
	log "github.com/sirupsen/logrus"
	"gopkg.in/mgo.v2/bson"
	"gopkg.in/validator.v2"
)

// EmailAddress type
type EmailAddress struct {
	EmailAddress string `json:"emailaddress" validate:"max=100"`
	Label        string `json:"label" validate:"regexp=^[a-zA-Z\\d\\-_\\s]+$"`
}

// PublicKey type
type PublicKey struct {
	PublicKey string `json:"publickey"`
	Label     string `json:"label" validate:"regexp=^[a-zA-Z\\d\\-_\\s]+$"`
}

// OIDCIdentity stores data about user's OIDC provider connection
type OIDCIdentity struct {
	Provider  string    `json:"provider" bson:"provider"`
	Subject   string    `json:"subject" bson:"subject"`
	Email     string    `json:"email" bson:"email"`
	LastLogin time.Time `json:"lastlogin" bson:"lastlogin"`
}

// User type
type User struct {
	ID                     bson.ObjectId  `json:"-" bson:"_id,omitempty"`
	EmailAddresses         []EmailAddress `json:"emailaddresses"`
	Expire                 db.DateTime    `json:"-" bson:"expire,omitempty"`
	Phonenumbers           []Phonenumber  `json:"phonenumbers"`
	PublicKeys             []PublicKey    `json:"publicKeys"`
	Username               string         `json:"username" validate:"min=2,max=30,regexp=^[a-z0-9]+$"`
	Firstname              string         `json:"firstname"`
	Lastname               string         `json:"lastname"`
	PPAcceptanceTimestamp  int64          `json:"privacy_policy_timestamp"`
	PPVersion              int            `json:"privacy_policy_version"`
	TCAcceptanceTimestamp  int64          `json:"terms_and_conditions_timestamp"`
	TCVersion              int            `json:"terms_and_conditions_version"`
	CPAcceptanceTimestamp  int64          `json:"cookie_policy_timestamp"`
	CPVersion              int            `json:"cookie_policy_version"`
	AUPAcceptanceTimestamp int64          `json:"acceptable_use_policy_timestamp"`
	AUPVersion             int            `json:"acceptable_use_policy_version"`
	DisableEmail2FA        bool           `json:"disable_email_2fa" bson:"disable_email_2fa"`
	OIDCIdentities         []OIDCIdentity `json:"oidcidentities,omitempty" bson:"oidcidentities,omitempty"`
}

// GetEmailAddressByLabel function
func (u *User) GetEmailAddressByLabel(label string) (email EmailAddress, err error) {
	for _, email = range u.EmailAddresses {
		if email.Label == label {
			return
		}
	}
	err = errors.New("Could not find EmailAddress with Label " + email.Label)
	return
}

// GetPhonenumberByLabel function
func (u *User) GetPhonenumberByLabel(label string) (phonenumber Phonenumber, err error) {
	for _, phonenumber = range u.Phonenumbers {
		if phonenumber.Label == label {
			return
		}
	}
	err = errors.New("Could not find Phonenumber with Label " + phonenumber.Label)
	return
}

// GetPublicKeyByLabel Gets the public key associated with this label
func (u *User) GetPublicKeyByLabel(label string) (publicKey PublicKey, err error) {
	for _, publicKey = range u.PublicKeys {
		if publicKey.Label == label {
			return
		}
	}
	err = errors.New("Could not find PublicKey with label " + label)
	return
}

// ValidateUsername function
func ValidateUsername(username string) bool {
	regex, _ := regexp.Compile(`^[a-z\d\-_\s]{2,30}$`)
	matches := regex.FindAllString(username, 2)
	return len(matches) == 1
}

// ValidateName function
func ValidateName(name string) bool {
	if len(name) > 60 || len(name) < 2 {
		return false
	}
	// This probably has terrible performance, but it'll have to do for now
	regex, _ := regexp.Compile(`[a-z\-_'\s]`)
	for _, r := range name {
		if !(regex.Match([]byte{byte(r)}) || unicode.IsLetter(unicode.ToLower(r))) {
			return false
		}
	}
	return true
}

// ValidatePhoneNumber function
func ValidatePhoneNumber(phoneNumber string) bool {
	regex := regexp.MustCompile(`^\+[0-9 \-]*$`)
	return regex.MatchString(phoneNumber)
}

// ValidateEmailAddress function
func ValidateEmailAddress(emailAddress string) bool {
	regex := regexp.MustCompile("[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$")
	return regex.MatchString(emailAddress) && len(emailAddress) <= 100
}

// Validate function
func (p PublicKey) Validate() bool {
	return validator.Validate(p) == nil && regexp.MustCompile(`^[a-zA-Z\d\-_\s]{2,50}$`).MatchString(p.Label)
}

// Validate function
func (e EmailAddress) Validate() bool {
	if err := validator.Validate(e); err != nil {
		log.Error("Failed to validate EmailAddress struct: ", err)
		return false
	}
	return regexp.MustCompile(`^[a-zA-Z\d\-_\s]{2,50}$`).MatchString(e.Label) && ValidateEmailAddress(e.EmailAddress)
}
