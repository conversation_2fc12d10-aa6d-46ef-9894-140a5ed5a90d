package user

import (
	"encoding/json"
	"net/http"
	"time"

	"fmt"
	"strings"

	"git.gig.tech/gig-meneja/iam/communication"
	"git.gig.tech/gig-meneja/iam/credentials/oauth2"
	"git.gig.tech/gig-meneja/iam/credentials/password"
	"git.gig.tech/gig-meneja/iam/credentials/totp"
	"git.gig.tech/gig-meneja/iam/db"
	"git.gig.tech/gig-meneja/iam/db/iyoid"
	"git.gig.tech/gig-meneja/iam/db/keystore"
	organizationDb "git.gig.tech/gig-meneja/iam/db/organization"
	"git.gig.tech/gig-meneja/iam/db/user"
	"git.gig.tech/gig-meneja/iam/db/user/apikey"
	validationdb "git.gig.tech/gig-meneja/iam/db/validation"
	"git.gig.tech/gig-meneja/iam/identityservice/invitations"
	"git.gig.tech/gig-meneja/iam/identityservice/organization"
	"git.gig.tech/gig-meneja/iam/oauthservice"
	"git.gig.tech/gig-meneja/iam/tools"
	"git.gig.tech/gig-meneja/iam/validation"
	"github.com/gorilla/context"
	"github.com/gorilla/mux"
	log "github.com/sirupsen/logrus"
	"gopkg.in/mgo.v2"
	"gopkg.in/yaml.v2"
)

const (
	maxIDGenerationAttempts = 5
)

// PhoneNumberValidation is the struct object for validating phone numbers
type PhoneNumberValidation struct {
	ValidationKey string `json:"validationkey"`
	Validated     bool   `json:"validated"`
}

// UsersAPI is the actual implementation of the /users api
type UsersAPI struct {
	SmsService                    communication.SMSService
	PhonenumberValidationService  *validation.IYOPhonenumberValidationService
	EmailService                  communication.EmailService
	EmailAddressValidationService *validation.IYOEmailAddressValidationService
}

func isUniquePhonenumber(user *user.User, number string, label string) (unique bool) {
	unique = true
	for _, phonenumber := range user.Phonenumbers {
		if phonenumber.Label != label && phonenumber.Phonenumber == number {
			unique = false
			return
		}
	}
	return
}

func isLastVerifiedPhoneNumber(user *user.User, number string, label string, r *http.Request) (last bool, err error) {
	last = false
	valMgr := validationdb.NewManager(r)
	validated, err := valMgr.IsPhonenumberValidated(user.Username, string(number))
	if err != nil {
		return
	}
	if validated {
		// check if this phone number is the last verified one
		uniquelabel := isUniquePhonenumber(user, number, label)
		hasotherverifiednumbers := false
		verifiednumbers, err := valMgr.GetByUsernameValidatedPhonenumbers(user.Username)
		if err != nil {
			return false, err

		}
		for _, verifiednumber := range verifiednumbers {
			if verifiednumber.Phonenumber != string(number) {
				hasotherverifiednumbers = true
				break

			}
		}
		if uniquelabel && !hasotherverifiednumbers {
			return true, nil
		}

	}
	return
}

// It is handler for POST /users
func (api UsersAPI) Post(w http.ResponseWriter, r *http.Request) {

	var u user.User

	if err := json.NewDecoder(r.Body).Decode(&u); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	userMgr := user.NewManager(r)
	if err := userMgr.Save(&u); err != nil {
		log.Error("ERROR while saving user:\n", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)

	json.NewEncoder(w).Encode(&u)
}

func (api UsersAPI) DeleteCurrentUser(w http.ResponseWriter, r *http.Request) {
	username := context.Get(r, "username").(string)
	userMgr := user.NewManager(r)
	orgMgr := organizationDb.NewManager(r)
	oauthMgr := oauthservice.NewManager(r)
	apikeyMgr := apikey.NewManager(r)
	valMgr := validationdb.NewManager(r)
	userobj, err := userMgr.GetByName(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	apikeys, err := apikeyMgr.GetByUser(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	for _, apikey := range apikeys {
		apikeyMgr.Delete(username, apikey.Label)
	}
	publickeys := userobj.PublicKeys
	for _, publicKey := range publickeys {
		userMgr.RemovePublicKey(username, publicKey.Label)
	}
	validated_emailaddresses, err := valMgr.GetByUsernameValidatedEmailAddress(username)
	if err != nil {
		log.Error("Failed to get users validated email addresses: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	for _, email := range validated_emailaddresses {
		if err = valMgr.RemoveValidatedEmailAddress(username, email.EmailAddress); err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}
	for _, email := range userobj.EmailAddresses {
		if err = userMgr.RemoveEmail(username, email.Label); err != nil {
			log.Error("Error while deleting email", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}
	for _, phone_number := range userobj.Phonenumbers {
		if err := userMgr.RemovePhone(username, phone_number.Label); err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}
	orgs, err := orgMgr.AllByUserChain(username)
	if err != nil {
		log.Error("Failed to load all organizations the user belongs to: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	for _, org := range orgs {
		org_details, err := orgMgr.GetByName(org)
		if handleServerError(w, "loading organization", err) {
			return
		}

		err = orgMgr.RemoveUser(org, username)
		if handleServerError(w, "removing user from organization", err) {
			return
		}
		err = userMgr.DeleteAuthorization(username, org)
		if handleServerError(w, "removing authorization", err) {
			return
		}
		err = oauthMgr.RemoveOrganizationScopes(org, username)
		if handleServerError(w, "removing organization scopes", err) {
			return
		}
		if len(org_details.Owners) == 1 && org_details.Owners[0] == username {
			suborganizations, err := orgMgr.GetSubOrganizations(org)
			if handleServerError(w, "fetching suborganizations", err) {
				return
			}
			for _, sub_org := range suborganizations {
				if err = organization.ActualOrganizationDeletion(r, sub_org.Globalid); err != nil {
					handleServerError(w, "deleting organization", err)
					return
				}
			}
			if err = organization.ActualOrganizationDeletion(r, org_details.Globalid); err != nil {
				handleServerError(w, "deleting organization", err)
				return
			}
		}
	}
	userMgr.Delete(userobj)

}

// GetUser is handler for GET /users/{username}
func (api UsersAPI) GetUser(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]

	userMgr := user.NewManager(r)

	usr, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(usr)
}

// RegisterNewEmailAddress is the handler for POST /users/{username}/emailaddresses
// Register a new email address
func (api UsersAPI) RegisterNewEmailAddress(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	body := user.EmailAddress{}
	lang := r.FormValue("lang")
	if lang == "" {
		lang = organization.DefaultLanguage
	}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil || !body.Validate() {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	// Convert email address to lowercase
	body.EmailAddress = strings.ToLower(body.EmailAddress)

	if !body.Validate() {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	userMgr := user.NewManager(r)
	u, err := userMgr.GetByName(username)
	if handleServerError(w, "getting user by name", err) {
		return
	}

	if _, err := u.GetEmailAddressByLabel(body.Label); err == nil {
		writeErrorResponse(w, http.StatusConflict, "duplicate_label")
		return
	}
	for _, email := range u.EmailAddresses {
		if email.EmailAddress == body.EmailAddress {
			writeErrorResponse(w, http.StatusConflict, "Email already exists")
			return
		}
	}
	err = userMgr.SaveEmail(username, body)
	if handleServerError(w, "saving email", err) {
		return
	}

	valMgr := validationdb.NewManager(r)
	validated, err := valMgr.IsEmailAddressValidated(username, body.EmailAddress)
	if handleServerError(w, "checking if email address is validated", err) {
		return
	}
	if !validated {
		validationKey, err := api.EmailAddressValidationService.RequestValidation(r, username, body.EmailAddress, fmt.Sprintf("https://%s/emailvalidation", r.Host), lang)
		if err != nil {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		response := struct {
			ValidationKey string `json:"validationkey"`
		}{
			ValidationKey: validationKey,
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		w.WriteHeader(http.StatusOK)
		return
	}
	w.WriteHeader(http.StatusCreated)
}

// UpdateEmailAddress is the handler for PUT /users/{username}/emailaddresses/{label}
// Updates the label and/or value of an email address
func (api UsersAPI) UpdateEmailAddress(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	oldlabel := mux.Vars(r)["label"]
	lang := r.FormValue("lang")
	if lang == "" {
		lang = organization.DefaultLanguage
	}

	var body user.EmailAddress
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	// Email address to all lowercase
	body.EmailAddress = strings.ToLower(body.EmailAddress)
	if !body.Validate() {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	userMgr := user.NewManager(r)
	u, err := userMgr.GetByName(username)
	if err != nil {
		log.Error("failed to get user by username: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	oldEmail, err := u.GetEmailAddressByLabel(oldlabel)
	if err != nil {
		log.Debug("Changing email address with non existing label")
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	valMgr := validationdb.NewManager(r)
	oldEmailValidated, err := valMgr.IsEmailAddressValidated(username, oldEmail.EmailAddress)
	if err != nil {
		log.Error("Failed to check if email address is verified for user: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if oldEmail.EmailAddress != body.EmailAddress && oldEmailValidated {
		log.Debug("Trying to change validated email address")
		http.Error(w, http.StatusText(http.StatusPreconditionFailed), http.StatusPreconditionFailed)
		return
	}

	if oldlabel != body.Label {
		if _, err = u.GetEmailAddressByLabel(body.Label); err == nil {
			http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
			return
		}
	}

	if err = userMgr.SaveEmail(username, body); err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	if oldlabel != body.Label {
		if err = userMgr.RemoveEmail(username, oldlabel); err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}
	validated, err := valMgr.IsEmailAddressValidated(username, body.EmailAddress)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if !validated {
		_, err = api.EmailAddressValidationService.RequestValidation(r, username, body.EmailAddress, fmt.Sprintf("https://%s/emailvalidation", r.Host), lang)
	}

	w.Header().Set("Content-Type", "application/json")

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(body)
}

// ValidateEmailAddress is the handler for POST /users/{username}/emailaddress/{label}/validate
func (api UsersAPI) ValidateEmailAddress(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	userMgr := user.NewManager(r)
	lang := r.FormValue("lang")
	if lang == "" {
		lang = organization.DefaultLanguage
	}
	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	email, err := userobj.GetEmailAddressByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	validationKey, err := api.EmailAddressValidationService.RequestValidation(r, username, email.EmailAddress, fmt.Sprintf("https://%s/emailvalidation", r.Host), lang)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	response := struct {
		ValidationKey string `json:"validationkey"`
	}{
		ValidationKey: validationKey,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
	w.WriteHeader(http.StatusOK)
}

// VerifyEmailAddress is the handler for PUT /users/{username}/emailaddress/{label}/validate
func (api UsersAPI) VerifyEmailAddress(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	userMgr := user.NewManager(r)

	values := struct {
		Emailcode     string `json:"emailcode"`
		ValidationKey string `json:"validationkey"`
	}{}
	if err := json.NewDecoder(r.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the ProcessEmailConfirmation request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	_, err = userobj.GetEmailAddressByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	err = api.EmailAddressValidationService.ConfirmValidation(r, values.ValidationKey, values.Emailcode)
	if err != nil {
		log.Debug(err)
		if err == validation.ErrInvalidCode || err == validation.ErrInvalidOrExpiredKey {
			writeErrorResponse(w, 422, err.Error())
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	userMgr.RemoveExpireDate(username)
	w.WriteHeader(http.StatusNoContent)

}

// ListEmailAddresses is the handler for GET /users/{username}/emailaddresses
func (api UsersAPI) ListEmailAddresses(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	validated := strings.Contains(r.URL.RawQuery, "validated")
	userMgr := user.NewManager(r)
	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	var emails []user.EmailAddress
	if validated {
		emails, err = api.getValidatedEmails(r, *userobj)
		if handleServerError(w, "getting validated emails", err) {
			return
		}
	} else {
		emails = userobj.EmailAddresses
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(emails)
}

func (api UsersAPI) getValidatedEmails(r *http.Request, userobj user.User) ([]user.EmailAddress, error) {
	emails := make([]user.EmailAddress, 0)
	valMngr := validationdb.NewManager(r)
	validatedEmails, err := valMngr.GetByUsernameValidatedEmailAddress(userobj.Username)
	if err == nil {
		for _, email := range userobj.EmailAddresses {
			for _, validatedEmail := range validatedEmails {
				if email.EmailAddress == validatedEmail.EmailAddress {
					emails = append(emails, email)
					break
				}
			}
		}
	}
	return emails, err
}

func (api UsersAPI) getValidatedPhones(r *http.Request, userobj user.User) ([]user.Phonenumber, error) {
	phones := make([]user.Phonenumber, 0)
	valMngr := validationdb.NewManager(r)
	validatedPhones, err := valMngr.GetByUsernameValidatedPhonenumbers(userobj.Username)
	if err == nil {
		for _, phone := range userobj.Phonenumbers {
			for _, validatedPhone := range validatedPhones {
				if phone.Phonenumber == validatedPhone.Phonenumber {
					phones = append(phones, phone)
					break
				}
			}
		}
	}
	return phones, err
}

// DeleteEmailAddress is the handler for DELETE /users/{username}/emailaddresses/{label}
// Removes an email address
func (api UsersAPI) DeleteEmailAddress(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]

	userMgr := user.NewManager(r)
	valMgr := validationdb.NewManager(r)

	u, err := userMgr.GetByName(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	email, err := u.GetEmailAddressByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	if len(u.EmailAddresses) == 1 {
		http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
		return
	}

	addresses, err := valMgr.GetByUsernameValidatedEmailAddress(username)
	if err != nil {
		log.Error("Failed to get users validated email addresses: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if len(addresses) == 1 && addresses[0].EmailAddress == email.EmailAddress {
		log.Debug("Can't remove last validated email address")
		http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
		return
	}

	if err = userMgr.RemoveEmail(username, label); err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if err = valMgr.RemoveValidatedEmailAddress(username, email.EmailAddress); err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)

}

// UpdatePassword is the handler for PUT /users/{username}/password
func (api UsersAPI) UpdatePassword(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	body := struct {
		Currentpassword string `json:"currentpassword"`
		Newpassword     string `json:"newpassword"`
	}{}
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	userMgr := user.NewManager(r)
	exists, err := userMgr.Exists(username)
	if !exists || err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	passwordMgr := password.NewManager(r)
	passwordok, err := passwordMgr.Validate(username, body.Currentpassword)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if !passwordok {
		writeErrorResponse(w, 422, "incorrect_password")
		return
	}
	err = passwordMgr.Save(username, body.Newpassword)
	if err != nil {
		writeErrorResponse(w, 422, err.Error())
		return
	}
	w.WriteHeader(http.StatusNoContent)
}

// function for splitting organizationss from roles in authorization organizations
func modifiyOrgs(orgs []string) OrganizationsInfo {
	// this function takes a list of sorted organizations and roles
	// we create this list of struct to add a struct of every org and its roles
	roles := []string{}
	globalIds := []string{}
	for _, org := range orgs {
		if strings.Contains(org, "roles") {
			roles = append(roles, org)
		} else {
			globalIds = append(globalIds, org)
		}
	}
	OrganizationsInfo := OrganizationsInfo{
		GlobalIds: globalIds,
		Roles:     roles,
	}
	return OrganizationsInfo
}

// GetUserInformation is the handler for GET /users/{username}/info
func (api UsersAPI) GetUserInformation(w http.ResponseWriter, r *http.Request) {
	username, validUsername := context.Get(r, "username").(string)
	if !validUsername {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	userMgr := user.NewManager(r)

	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	requestingClient, validClient := context.Get(r, "client_id").(string)
	if !validClient {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	availableScopes, _ := context.Get(r, "availablescopes").(string)
	isAdmin := oauth2.CheckScopes([]string{"user:admin"}, oauth2.SplitScopeString(availableScopes))

	authorization, err := userMgr.GetAuthorization(username, requestingClient)
	if handleServerError(w, "getting authorization", err) {
		return
	}
	//Create an administrator authorization
	// TODO: rework for non admin scope in client credentials
	// if authorization is nil we are already assured that this is client credentials due to the middleware
	authorizedScopes := oauth2.SplitScopeString(availableScopes)
	if authorization == nil {
		authorization = &user.Authorization{
			// TODO: check bool values
			Name:                    false,
			EmailAddresses:          []user.AuthorizationMap{},
			ValidatedEmailAddresses: []user.AuthorizationMap{},
			Phonenumbers:            []user.AuthorizationMap{},
			ValidatedPhonenumbers:   []user.AuthorizationMap{},
			PublicKeys:              []user.AuthorizationMap{},
			Organizations:           []string{},
		}
		_, all := filterAuthorizedInfo(authorizedScopes, "user:name")
		if isAdmin || all {
			authorization.Name = true
		}
		for _, a := range userobj.EmailAddresses {
			labels, all := filterAuthorizedInfo(authorizedScopes, "user:email")
			if all || isAdmin {
				authorization.EmailAddresses = append(authorization.EmailAddresses, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				continue
			}
			for _, label := range labels {
				if label == a.Label {
					authorization.EmailAddresses = append(authorization.EmailAddresses, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				}
			}
		}
		for _, a := range userobj.EmailAddresses {
			labels, all := filterAuthorizedInfo(authorizedScopes, "user:validated:email")
			if all || isAdmin {
				authorization.ValidatedEmailAddresses = append(authorization.ValidatedEmailAddresses, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				continue
			}
			for _, label := range labels {
				if label == a.Label {
					authorization.ValidatedEmailAddresses = append(authorization.ValidatedEmailAddresses, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				}
			}
		}
		for _, a := range userobj.Phonenumbers {
			labels, all := filterAuthorizedInfo(authorizedScopes, "user:phone")
			if all || isAdmin {
				authorization.Phonenumbers = append(authorization.Phonenumbers, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				continue
			}
			for _, label := range labels {
				if label == a.Label {
					authorization.Phonenumbers = append(authorization.Phonenumbers, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				}
			}
		}
		for _, a := range userobj.Phonenumbers {
			labels, all := filterAuthorizedInfo(authorizedScopes, "user:validated:phone")
			if all || isAdmin {
				authorization.ValidatedPhonenumbers = append(authorization.ValidatedPhonenumbers, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				continue
			}
			for _, label := range labels {
				if label == a.Label {
					authorization.ValidatedPhonenumbers = append(authorization.ValidatedPhonenumbers, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				}
			}
		}
		for _, a := range userobj.PublicKeys {
			labels, all := filterAuthorizedInfo(authorizedScopes, "user:publickey")
			if all || isAdmin {
				authorization.PublicKeys = append(authorization.PublicKeys, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				continue
			}
			for _, label := range labels {
				if label == a.Label {
					authorization.PublicKeys = append(authorization.PublicKeys, user.AuthorizationMap{RequestedLabel: a.Label, RealLabel: a.Label})
				}
			}
		}
		labels, _ := filterAuthorizedInfo(authorizedScopes, "user:memberof")
		if len(labels) > 0 || isAdmin {
			userOrgs, err := organizationDb.NewManager(r).AllByUserChain(username)
			if handleServerError(w, "Getting user organizations", err) {
				return
			}
			for _, a := range userOrgs {
				if isAdmin {
					authorization.Organizations = append(authorization.Organizations, a)
					continue
				}
				for _, l := range labels {
					if l == a {
						authorization.Organizations = append(authorization.Organizations, a)
					}
				}
			}
		}

	}
	if authorization == nil {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return
	}

	respBody := &Userview{
		Username:                userobj.Username,
		EmailAddresses:          []user.EmailAddress{},
		ValidatedEmailAddresses: []user.EmailAddress{},
		Phonenumbers:            []user.Phonenumber{},
		ValidatedPhonenumbers:   []user.Phonenumber{},
		OwnerOf: user.OwnerOf{
			EmailAddresses: []string{},
		},
		PublicKeys:    []user.PublicKey{},
		Organizations: OrganizationsInfo{},
	}

	if authorization.Name {
		respBody.Firstname = userobj.Firstname
		respBody.Lastname = userobj.Lastname
	}

	if authorization.EmailAddresses != nil {
		for _, emailmap := range authorization.EmailAddresses {
			email, err := userobj.GetEmailAddressByLabel(emailmap.RealLabel)
			if err == nil {
				newemail := user.EmailAddress{
					Label:        emailmap.RequestedLabel,
					EmailAddress: email.EmailAddress,
				}
				respBody.EmailAddresses = append(respBody.EmailAddresses, newemail)
			} else {
				log.Debug(err)
			}
		}
	}
	if authorization.Phonenumbers != nil {
		for _, phonemap := range authorization.Phonenumbers {
			phonenumber, err := userobj.GetPhonenumberByLabel(phonemap.RealLabel)
			if err == nil {
				newnumber := user.Phonenumber{
					Label:       phonemap.RequestedLabel,
					Phonenumber: phonenumber.Phonenumber,
				}
				respBody.Phonenumbers = append(respBody.Phonenumbers, newnumber)
			} else {
				log.Debug(err)
			}
		}
	}

	if authorization.PublicKeys != nil {
		for _, publicKeyMap := range authorization.PublicKeys {
			publicKey, err := userobj.GetPublicKeyByLabel(publicKeyMap.RealLabel)
			if err == nil {
				publicKey.Label = publicKeyMap.RequestedLabel
				respBody.PublicKeys = append(respBody.PublicKeys, publicKey)
			} else {
				log.Debug(err)
			}
		}
	}

	if authorization.Organizations != nil {
		OrganizationsInfo := modifiyOrgs(authorization.Organizations)
		respBody.Organizations = OrganizationsInfo
	}

	valMgr := validationdb.NewManager(r)
	if authorization.ValidatedEmailAddresses != nil {
		for _, validatedEmailMap := range authorization.ValidatedEmailAddresses {
			email, err := userobj.GetEmailAddressByLabel(validatedEmailMap.RealLabel)
			if err == nil {
				validated, err := valMgr.IsEmailAddressValidated(username, email.EmailAddress)
				if err != nil {
					log.Error("Failed to verify if email address is validated for this user: ", err)
					continue
				}
				if !validated {
					continue
				}
				email.Label = validatedEmailMap.RequestedLabel
				respBody.ValidatedEmailAddresses = append(respBody.ValidatedEmailAddresses, email)
			} else {
				log.Debug(err)
			}
		}

		if authorization.ValidatedPhonenumbers != nil {
			for _, validatedPhoneMap := range authorization.ValidatedPhonenumbers {
				phone, err := userobj.GetPhonenumberByLabel(validatedPhoneMap.RealLabel)
				if err == nil {
					validated, err := valMgr.IsPhonenumberValidated(username, phone.Phonenumber)
					if err != nil {
						log.Error("Failed to verify if phone number is validated for this user: ", err)
						continue
					}
					if !validated {
						continue
					}
					phone.Label = validatedPhoneMap.RequestedLabel
					respBody.ValidatedPhonenumbers = append(respBody.ValidatedPhonenumbers, phone)
				} else {
					log.Debug(err)
				}
			}
		}
	}

	if authorization.OwnerOf.EmailAddresses != nil {
		respBody.OwnerOf.EmailAddresses = authorization.OwnerOf.EmailAddresses
	}
	path := r.URL.Path
	if path[len(path)-4:] == "yaml" {
		apikeyMgr := apikey.NewManager(r)
		apikeys, err := apikeyMgr.GetByUser(username)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
			return
		}
		if apikeys == nil {
			apikeys = []apikey.APIKey{}
		}
		respBody := &AllUserData{
			Userview: *respBody,
			ApiKeys:  apikeys,
		}
		yaml_data, err := yaml.Marshal(respBody)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/yaml")
		json.NewEncoder(w).Encode(string(yaml_data))
	} else {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(respBody)
	}
}

// filterAuthorizedInfo filters out the labels of a specific scope. If no label is added, we assume all of them are authorized
func filterAuthorizedInfo(authorizedScopes []string, baseScope string) ([]string, bool) {
	all := false
	labels := []string{}
	for _, scope := range authorizedScopes {
		if scope == baseScope {
			all = true
			continue
		}
		if strings.HasPrefix(scope, baseScope) {
			labels = append(labels, strings.TrimPrefix(scope, baseScope+":"))
		}
	}
	return labels, all
}

// RegisterNewPhonenumber is the handler for POST /users/{username}/phonenumbers
// Register a new phonenumber
func (api UsersAPI) RegisterNewPhonenumber(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	userMgr := user.NewManager(r)
	lang := r.FormValue("lang")
	if lang == "" {
		lang = "en"
	}

	u, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	body := user.Phonenumber{}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if !body.Validate() {
		log.Debug("Invalid phonenumber: ", body.Phonenumber)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	//Check if this label is already used
	_, err = u.GetPhonenumberByLabel(body.Label)
	if err == nil {
		http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
		return
	}

	if err := userMgr.SavePhone(username, body); err != nil {
		log.Error("ERROR while saving a phonenumber - ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	valMgr := validationdb.NewManager(r)
	validated, err := valMgr.IsPhonenumberValidated(username, body.Phonenumber)
	if !validated {
		phonenumber := user.Phonenumber{Label: body.Label, Phonenumber: body.Phonenumber}
		validationKey, err := api.PhonenumberValidationService.RequestValidation(r, username, phonenumber, fmt.Sprintf("https://%s/phonevalidation", r.Host), lang)
		if err != nil {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		response := PhoneNumberValidation{
			ValidationKey: validationKey,
			Validated:     false,
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		w.WriteHeader(http.StatusOK)
		return
	}
	response := PhoneNumberValidation{
		Validated: true,
	}
	// respond with created phone number.
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)

	json.NewEncoder(w).Encode(response)
}

// GetUserPhoneNumbers is the handler for GET /users/{username}/phonenumbers
func (api UsersAPI) GetUserPhoneNumbers(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	validated := strings.Contains(r.URL.RawQuery, "validated")
	userMgr := user.NewManager(r)

	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	var phonenumbers []user.Phonenumber
	if validated {
		phonenumbers = make([]user.Phonenumber, 0)
		valMngr := validationdb.NewManager(r)
		validatednumbers, err := valMngr.GetByUsernameValidatedPhonenumbers(username)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		for _, number := range userobj.Phonenumbers {
			for _, validatednumber := range validatednumbers {
				if number.Phonenumber == validatednumber.Phonenumber {
					phonenumbers = append(phonenumbers, number)
					break
				}
			}
		}
	} else {
		phonenumbers = userobj.Phonenumbers
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(phonenumbers)
}

// GetUserPhonenumberByLabel is the handler for GET /users/{username}/phonenumbers/{label}
func (api UsersAPI) GetUserPhonenumberByLabel(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	userMgr := user.NewManager(r)

	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	phonenumber, err := userobj.GetPhonenumberByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(phonenumber)
}

// ValidatePhoneNumber is the handler for POST /users/{username}/phonenumbers/{label}/validate
func (api UsersAPI) ValidatePhoneNumber(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	lang := r.FormValue("lang")
	if lang == "" {
		lang = "en"
	}

	userMgr := user.NewManager(r)

	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	phonenumber, err := userobj.GetPhonenumberByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	validationKey := ""
	validationKey, err = api.PhonenumberValidationService.RequestValidation(r, username, phonenumber, fmt.Sprintf("https://%s/phonevalidation", r.Host), lang)
	response := PhoneNumberValidation{
		ValidationKey: validationKey,
		Validated:     false,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
	w.WriteHeader(http.StatusOK)
}

// VerifyPhoneNumber is the handler for PUT /users/{username}/phonenumbers/{label}/validate
func (api UsersAPI) VerifyPhoneNumber(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	userMgr := user.NewManager(r)

	values := struct {
		Smscode       string `json:"smscode"`
		ValidationKey string `json:"validationkey"`
	}{}
	if err := json.NewDecoder(r.Body).Decode(&values); err != nil {
		log.Debug("Error decoding the ProcessPhonenumberConfirmation request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	_, err = userobj.GetPhonenumberByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	err = api.PhonenumberValidationService.ConfirmValidation(r, values.ValidationKey, values.Smscode)
	if err != nil {
		log.Debug(err)
		if err == validation.ErrInvalidCode || err == validation.ErrInvalidOrExpiredKey {
			writeErrorResponse(w, 422, err.Error())
		} else {
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		}
		return
	}
	userMgr.RemoveExpireDate(username)
	w.WriteHeader(http.StatusNoContent)
}

// UpdatePhonenumber is the handler for PUT /users/{username}/phonenumbers/{label}
// Update the label and/or value of an existing phonenumber.
func (api UsersAPI) UpdatePhonenumber(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	oldlabel := mux.Vars(r)["label"]

	body := user.Phonenumber{}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if !body.Validate() {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	userMgr := user.NewManager(r)

	u, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	oldnumber, err := u.GetPhonenumberByLabel(oldlabel)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	if oldlabel != body.Label {
		// Check if there already is another phone number with the new label
		_, err := u.GetPhonenumberByLabel(body.Label)
		if err == nil {
			writeErrorResponse(w, http.StatusConflict, "duplicate_label")
			return
		}
	}

	valMgr := validationdb.NewManager(r)
	if oldnumber.Phonenumber != body.Phonenumber {
		validatedPhone, err := valMgr.GetByPhoneNumber(oldnumber.Phonenumber)
		if err != nil && !db.IsNotFound(err) {
			log.Error("ERROR while updating phone number - ", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		if validatedPhone.Username == username {
			log.Debug("Try to modify validated phone number")
			http.Error(w, "cannot_modify_validated_phone", http.StatusPreconditionFailed)
			return
		}
		last, err := isLastVerifiedPhoneNumber(u, oldnumber.Phonenumber, oldlabel, r)
		if err != nil {
			log.Error("ERROR while verifying last verified number - ", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		if last {
			writeErrorResponse(w, http.StatusConflict, "cannot_delete_last_verified_phone_number")
			return
		}
	}

	if err = userMgr.SavePhone(username, body); err != nil {
		log.Error("ERROR while saving phonenumber - ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	if oldlabel != body.Label {
		if err := userMgr.RemovePhone(username, oldlabel); err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}

	if oldnumber.Phonenumber != body.Phonenumber && isUniquePhonenumber(u, oldnumber.Phonenumber, oldlabel) {
		valMgr.RemoveValidatedPhonenumber(username, oldnumber.Phonenumber)
	}

	w.Header().Set("Content-Type", "application/json")

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(body)

}

// DeletePhonenumber is the handler for DELETE /users/{username}/phonenumbers/{label}
// Removes a phonenumber
func (api UsersAPI) DeletePhonenumber(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	userMgr := user.NewManager(r)
	valMgr := validationdb.NewManager(r)

	usr, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	number, err := usr.GetPhonenumberByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	last, err := isLastVerifiedPhoneNumber(usr, number.Phonenumber, label, r)
	if err != nil {
		log.Error("ERROR while checking if number can be deleted:\n", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return

	}
	if last {
		// Even with totp enforce a validated phone number
		writeErrorResponse(w, http.StatusConflict, "cannot_delete_last_verified_phone_number")
		return
	}

	// check if the phonenumber is unique or if there are duplicates
	uniqueNumber := isUniquePhonenumber(usr, number.Phonenumber, label)

	if err := userMgr.RemovePhone(username, label); err != nil {
		log.Error("ERROR while saving user:\n", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// only remove the phonenumber from the validatedphonenumber collection if there are no duplicates
	if uniqueNumber {
		if err := valMgr.RemoveValidatedPhonenumber(username, number.Phonenumber); err != nil {
			log.Error("ERROR while saving user:\n", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}

	w.WriteHeader(http.StatusNoContent)
}

// GetNotifications is handler for GET /users/{username}/notifications
// Get the list of notifications, these are pending invitations or approvals
func (api UsersAPI) GetNotifications(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]

	type NotificationList struct {
		Approvals               []invitations.JoinOrganizationInvitation `json:"approvals"`
		Invitations             []invitations.JoinOrganizationInvitation `json:"invitations"`
		MissingScopes           []organizationDb.MissingScope            `json:"missingscopes"`
		OrganizationInvitations []invitations.JoinOrganizationInvitation `json:"organizationinvitations"`
	}
	var notifications NotificationList

	invitationMgr := invitations.NewInvitationManager(r)
	valMgr := validationdb.NewManager(r)

	userOrgRequests, err := invitationMgr.FilterByUser(username, "pending")
	if handleServerError(w, "getting invitations by user", err) {
		return
	}

	// Add the invites for the users verified phone numbers. This is required if the invite
	// was added before the phone number was verified, and no invite sms was send
	validatedPhonenumbers, err := valMgr.GetByUsernameValidatedPhonenumbers(username)
	if handleServerError(w, "getting verified phone numbers", err) {
		return
	}
	for _, number := range validatedPhonenumbers {
		phonenumberRequests, err := invitationMgr.FilterByPhonenumber(number.Phonenumber, "pending")
		if handleServerError(w, "getting invitations by user for phonenumber", err) {
			return
		}
		userOrgRequests = append(userOrgRequests, phonenumberRequests...)
	}

	// Add the invites for the users verified email addresses. This is required if the invite
	// was added before the email address was verified, and no invite email was send
	validatedEmailaddresses, err := valMgr.GetByUsernameValidatedEmailAddress(username)
	if handleServerError(w, "getting verified email addresses", err) {
		return
	}
	for _, email := range validatedEmailaddresses {
		emailRequests, err := invitationMgr.FilterByEmail(email.EmailAddress, "pending")
		if handleServerError(w, "getting invitations by user for email", err) {
			return
		}
		for _, emailRequest := range emailRequests {
			alreadyFound := false
			for _, inv := range userOrgRequests {
				if inv.Organization == emailRequest.Organization {
					alreadyFound = true
					break
				}
			}
			if !alreadyFound {
				userOrgRequests = append(userOrgRequests, emailRequest)
			}
		}
	}

	// Add the invites for the organizations where this user is an owner
	orgMgr := organizationDb.NewManager(r)

	orgs, err := orgMgr.AllByUser(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	var ownedOrgs []string

	for _, org := range orgs {
		if exists(username, org.Owners) {
			ownedOrgs = append(ownedOrgs, org.Globalid)
		}
	}

	orgInvites := make([]invitations.JoinOrganizationInvitation, 0)

	for _, org := range ownedOrgs {
		invites, err := invitationMgr.GetOpenOrganizationInvites(org)
		if err != nil {
			log.Error("Error while loading all invites where the organization ", org, " is invited")
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		orgInvites = append(orgInvites, invites...)
	}

	notifications.Invitations = userOrgRequests
	notifications.OrganizationInvitations = orgInvites
	// TODO: Get Approvals
	notifications.Approvals = []invitations.JoinOrganizationInvitation{}
	extraOrganizations := []string{}
	for _, invitation := range notifications.Invitations {
		extraOrganizations = append(extraOrganizations, invitation.Organization)
	}
	err, notifications.MissingScopes = getMissingScopesForOrganizations(r, username, extraOrganizations)
	if handleServerError(w, "getting missing scopes", err) {
		return
	}
	w.Header().Set("Content-type", "application/json")
	json.NewEncoder(w).Encode(&notifications)
}

func getMissingScopesForOrganizations(r *http.Request, username string, extraOrganizations []string) (error, []organizationDb.MissingScope) {
	orgMgr := organizationDb.NewManager(r)
	userMgr := user.NewManager(r)
	err, organizations := orgMgr.ListByUserOrGlobalID(username, extraOrganizations)

	if err != nil {
		return err, nil
	}
	authorizations, err := userMgr.GetAuthorizationsByUser(username)
	if err != nil {
		return err, nil
	}
	missingScopes := []organizationDb.MissingScope{}
	authorizationsMap := make(map[string]user.Authorization)
	for _, authorization := range authorizations {
		authorizationsMap[authorization.Username] = authorization
	}
	for _, organization := range organizations {
		scopes := []string{}
		for _, requiredScope := range organization.RequiredScopes {
			hasScope := false
			if authorization, hasKey := authorizationsMap[username]; hasKey {
				hasScope = requiredScope.IsAuthorized(authorization)
			} else {
				hasScope = false
			}
			if !hasScope {
				scopes = append(scopes, requiredScope.Scope)
			}
		}
		if len(scopes) > 0 {
			missingScope := organizationDb.MissingScope{
				Scopes:       scopes,
				Organization: organization.Globalid,
			}
			missingScopes = append(missingScopes, missingScope)
		}
	}
	return nil, missingScopes
}

// usernameorganizationsGet is the handler for GET /users/{username}/organizations
// Get the list organizations a user is owner of member of
func (api UsersAPI) usernameorganizationsGet(w http.ResponseWriter, r *http.Request) {

}

// GetAllAuthorizations is the handler for GET /users/{username}/authorizations
// Get the list of authorizations.
func (api UsersAPI) GetAllAuthorizations(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]

	userMgr := user.NewManager(r)

	authorizations, err := userMgr.GetAuthorizationsByUser(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-type", "application/json")
	json.NewEncoder(w).Encode(authorizations)

}

// GetAuthorization is the handler for GET /users/{username}/authorizations/{grantedTo}
// Get the authorization for a specific organization.
func (api UsersAPI) GetAuthorization(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	grantedTo := mux.Vars(r)["grantedTo"]

	userMgr := user.NewManager(r)

	authorization, err := userMgr.GetAuthorization(username, grantedTo)
	if handleServerError(w, "Getting authorization by user", err) {
		return
	}
	if authorization == nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	w.Header().Set("Content-type", "application/json")
	json.NewEncoder(w).Encode(authorization)
}

func FilterAuthorizationMaps(s []user.AuthorizationMap) []user.AuthorizationMap {
	var p []user.AuthorizationMap
	for _, v := range s {
		if v.RealLabel != "" {
			p = append(p, v)
		}
	}
	return p
}

func FilterOwnerOf(s user.OwnerOf, verifiedEmails []user.EmailAddress) user.OwnerOf {
	var o user.OwnerOf
	for _, verifiedEmail := range verifiedEmails {
		for _, mail := range s.EmailAddresses {
			if mail == verifiedEmail.EmailAddress {
				o.EmailAddresses = append(o.EmailAddresses, verifiedEmail.EmailAddress)
			}
		}
	}
	return o
}

// FilterValidatedEmail removes email addresses which are not validated
func FilterValidatedEmails(authorizedMails []user.AuthorizationMap, verifiedMails []user.EmailAddress) []user.AuthorizationMap {
	var e []user.AuthorizationMap
	for _, authorizedMail := range authorizedMails {
		for _, verifiedMail := range verifiedMails {
			if authorizedMail.RealLabel == verifiedMail.Label {
				e = append(e, authorizedMail)
				break
			}
		}
	}
	return e
}

// FilterValidatedPhones removes phone numbers which are not validated
func FilterValidatedPhones(authorizedPhones []user.AuthorizationMap, verifiedPhones []user.Phonenumber) []user.AuthorizationMap {
	var p []user.AuthorizationMap
	for _, authorizedPhone := range authorizedPhones {
		for _, verifiedPhone := range verifiedPhones {
			if authorizedPhone.RealLabel == verifiedPhone.Label {
				p = append(p, authorizedPhone)
				break
			}
		}
	}
	return p
}

// UpdateAuthorization is the handler for PUT /users/{username}/authorizations/{grantedTo}
// Modify which information an organization is able to see.
func (api UsersAPI) UpdateAuthorization(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	grantedTo := mux.Vars(r)["grantedTo"]

	authorization := &user.Authorization{}

	if err := json.NewDecoder(r.Body).Decode(authorization); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	userMgr := user.NewManager(r)
	userobj, err := userMgr.GetByName(username)
	if handleServerError(w, "getting user by name", err) {
		return
	}
	verifiedEmails := []user.EmailAddress{}
	if len(authorization.OwnerOf.EmailAddresses) != 0 || len(authorization.ValidatedEmailAddresses) > 0 {
		verifiedEmails, err = api.getValidatedEmails(r, *userobj)
		if handleServerError(w, "getting verified emails", err) {
			return
		}
	}

	verifiedPhones := []user.Phonenumber{}
	if len(authorization.ValidatedPhonenumbers) > 0 {
		verifiedPhones, err = api.getValidatedPhones(r, *userobj)
		if handleServerError(w, "getting verified phone numbers", err) {
			return
		}
	}

	authorization.Username = username
	authorization.GrantedTo = grantedTo
	authorization.EmailAddresses = FilterAuthorizationMaps(authorization.EmailAddresses)
	authorization.ValidatedEmailAddresses = FilterValidatedEmails(FilterAuthorizationMaps(authorization.ValidatedEmailAddresses), verifiedEmails)
	authorization.Phonenumbers = FilterAuthorizationMaps(authorization.Phonenumbers)
	authorization.ValidatedPhonenumbers = FilterValidatedPhones(FilterAuthorizationMaps(authorization.ValidatedPhonenumbers), verifiedPhones)
	authorization.PublicKeys = FilterAuthorizationMaps(authorization.PublicKeys)
	authorization.OwnerOf = FilterOwnerOf(authorization.OwnerOf, verifiedEmails)

	existingAuth, err := userMgr.GetAuthorization(username, grantedTo)
	if handleServerError(w, "getting existing authorization", err) {
		return
	}
	if existingAuth != nil {
		// Merge authorizations
		authorization.Merge(existingAuth)
	}

	err = userMgr.UpdateAuthorization(authorization)
	if handleServerError(w, "updating authorization", err) {
		return
	}
	w.Header().Set("Content-type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(authorization)
}

// DeleteAuthorization is the handler for DELETE /users/{username}/authorizations/{grantedTo}
// Remove the authorization for an organization, the granted organization will no longer
// have access the user's information.
func (api UsersAPI) DeleteAuthorization(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	grantedTo := mux.Vars(r)["grantedTo"]

	userMgr := user.NewManager(r)

	err := userMgr.DeleteAuthorization(username, grantedTo)
	if handleServerError(w, "Delete authorization", err) {
		return
	}
	w.WriteHeader(http.StatusNoContent)
}

// getRequestingClientFromRequest validates if a see api call is valid for an organization
func getRequestingClientFromRequest(r *http.Request, w http.ResponseWriter, organizationGlobalID string, allowOnWebsite bool) (string, bool) {
	requestingClient, validClient := context.Get(r, "client_id").(string)
	if !validClient {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return requestingClient, false
	}
	if requestingClient == "iam" {
		if allowOnWebsite {
			requestingClient = organizationGlobalID
		} else {
			// This should never happen as the oauth 2  middleware should give a 403
			writeErrorResponse(w, http.StatusBadRequest, "This api call is not available when logged in via the website")
			return requestingClient, false
		}
	} else if requestingClient != organizationGlobalID {
		writeErrorResponse(w, http.StatusForbidden, "unauthorized_organization")
		return requestingClient, false
	}
	return requestingClient, true
}

func (api UsersAPI) AddAPIKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	body := struct {
		Label string
	}{}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	if !user.IsValidLabel(body.Label) {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	apikeyMgr := apikey.NewManager(r)
	// check if this is a free label
	existingKey, err := apikeyMgr.GetByUsernameAndLabel(username, body.Label)
	if handleServerError(w, "getting user api key", err) {
		return
	}
	if existingKey.Label != "" {
		http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
		return
	}
	apiKey := apikey.NewAPIKey(username, body.Label)
	apikeyMgr.Save(apiKey)
	w.Header().Set("Content-type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(apiKey)
}

func (api UsersAPI) GetAPIKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	apikeyMgr := apikey.NewManager(r)
	apiKey, err := apikeyMgr.GetByUsernameAndLabel(username, label)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	w.Header().Set("Content-type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(apiKey)
}

func (api UsersAPI) UpdateAPIKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	apikeyMgr := apikey.NewManager(r)
	body := struct {
		Label string
	}{}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	apiKey, err := apikeyMgr.GetByUsernameAndLabel(username, label)
	if handleServerError(w, "getting user api key", err) {
		return
	}
	if apiKey.Label == "" {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	// check if a key with the new label already exists
	dupKey, err := apikeyMgr.GetByUsernameAndLabel(username, body.Label)
	if handleServerError(w, "getting user api key", err) {
		return
	}
	if dupKey.Label != "" {
		http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
		return
	}

	apiKey.Label = body.Label
	err = apikeyMgr.Save(apiKey)
	if handleServerError(w, "saving api key with updated label", err) {
		return
	}
	w.WriteHeader(http.StatusNoContent)

}
func (api UsersAPI) DeleteAPIKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	apikeyMgr := apikey.NewManager(r)
	apikeyMgr.Delete(username, label)
	w.WriteHeader(http.StatusNoContent)
}

func (api UsersAPI) ListAPIKeys(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	apikeyMgr := apikey.NewManager(r)
	apikeys, err := apikeyMgr.GetByUser(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	if apikeys == nil {
		apikeys = []apikey.APIKey{}
	}
	w.Header().Set("Content-type", "application/json")
	w.WriteHeader(http.StatusOK)

	json.NewEncoder(w).Encode(apikeys)
}

// AddPublicKey Add a public key
func (api UsersAPI) AddPublicKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	body := user.PublicKey{}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if !strings.HasPrefix(body.PublicKey, "ssh-rsa AAAAB3NzaC1yc2E") {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	mgr := user.NewManager(r)

	usr, err := mgr.GetByName(username)
	if err != nil {
		if err == mgo.ErrNotFound {
			http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
			return
		}
		log.Error("Error while getting user: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	_, err = usr.GetPublicKeyByLabel(body.Label)
	if err == nil {
		http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
		return
	}

	err = mgr.SavePublicKey(username, body)
	if err != nil {
		log.Error("error while saving public key: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(body)
}

// GetPublicKey Get the public key associated with a label
func (api UsersAPI) GetPublicKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	userMgr := user.NewManager(r)

	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	publickey, err := userobj.GetPublicKeyByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(publickey)
}

// UpdatePublicKey Update the label and/or value of an existing public key.
func (api UsersAPI) UpdatePublicKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	oldlabel := mux.Vars(r)["label"]

	body := user.PublicKey{}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if !body.Validate() {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	if !strings.HasPrefix(body.PublicKey, "ssh-rsa AAAAB3NzaC1yc2E") {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	userMgr := user.NewManager(r)

	u, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	_, err = u.GetPublicKeyByLabel(oldlabel)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	if oldlabel != body.Label {
		// Check if there already is another public key with the new label
		_, err := u.GetPublicKeyByLabel(body.Label)
		if err == nil {
			writeErrorResponse(w, http.StatusConflict, "duplicate_label")
			return
		}
	}

	if err = userMgr.SavePublicKey(username, body); err != nil {
		log.Error("ERROR while saving public key - ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	if oldlabel != body.Label {
		if err := userMgr.RemovePublicKey(username, oldlabel); err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
	}

	w.Header().Set("Content-Type", "application/json")

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(body)

}

// DeletePublicKey Deletes a public key
func (api UsersAPI) DeletePublicKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	label := mux.Vars(r)["label"]
	userMgr := user.NewManager(r)

	usr, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	_, err = usr.GetPublicKeyByLabel(label)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	if err := userMgr.RemovePublicKey(username, label); err != nil {
		log.Error("ERROR while removing public key:\n", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// ListPublicKeys lists all public keys
func (api UsersAPI) ListPublicKeys(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	userMgr := user.NewManager(r)
	userobj, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	var publicKeys []user.PublicKey

	publicKeys = userobj.PublicKeys

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(publicKeys)
}

// GetKeyStore returns all the publickeys written to the user by an organizaton
func (api UsersAPI) GetKeyStore(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	globalid := context.Get(r, "client_id").(string)

	mgr := keystore.NewManager(r)
	keys, err := mgr.ListKeyStoreKeys(username, globalid)
	if err != nil && !db.IsNotFound(err) {
		log.Error("Failed to get keystore keys: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(keys)
}

// GetKeyStoreKey returns all specific publickey written to the user by an organizaton
func (api UsersAPI) GetKeyStoreKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	globalid := context.Get(r, "client_id").(string)
	label := mux.Vars(r)["label"]

	mgr := keystore.NewManager(r)

	key, err := mgr.GetKeyStoreKey(username, globalid, label)
	if db.IsNotFound(err) {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	if err != nil {
		log.Error("Failed to get keystore key: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(key)
}

// SaveKeyStoreKey returns all the publickeys written to the user by an organizaton
func (api UsersAPI) SaveKeyStoreKey(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	globalid := context.Get(r, "client_id").(string)

	body := keystore.KeyStoreKey{}

	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		log.Debug("Keystore key decoding failed: ", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	// set/update the username and globalid values to those from the authentication
	body.Username = username
	body.Globalid = globalid
	// set the keys timestamp
	body.KeyData.TimeStamp = db.DateTime(time.Now())

	if !body.Validate() {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	mgr := keystore.NewManager(r)

	// check if this user/organization already has a key under this label
	if _, err := mgr.GetKeyStoreKey(username, globalid, body.Label); err == nil {
		http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
		return
	}

	err := mgr.Create(&body)
	if err != nil {
		log.Error("error while saving keystore key: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	key, err := mgr.GetKeyStoreKey(username, globalid, body.Label)
	if err != nil {
		log.Error("error while retrieving keystore key: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(key)
}

// UpdateName is the handler for PUT /users/{username}/name
func (api UsersAPI) UpdateName(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	values := struct {
		Firstname string `json:"firstname"`
		Lastname  string `json:"lastname"`
	}{}
	if err := json.NewDecoder(r.Body).Decode(&values); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	userMgr := user.NewManager(r)
	exists, err := userMgr.Exists(username)
	if !exists || err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}
	err = userMgr.UpdateName(username, values.Firstname, values.Lastname)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusNoContent)
}

// GetTwoFAMethods is the handler for GET /users/{username}/twofamethods
// Get the possible two factor authentication methods
func (api UsersAPI) GetTwoFAMethods(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	userMgr := user.NewManager(r)
	userFromDB, err := userMgr.GetByName(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	response := struct {
		Totp            bool               `json:"totp"`
		Sms             []user.Phonenumber `json:"sms"`
		DisableEmail2FA bool               `json:"disable_email_2fa"`
	}{DisableEmail2FA: userFromDB.DisableEmail2FA}
	totpMgr := totp.NewManager(r)
	response.Totp, err = totpMgr.HasTOTP(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	valMgr := validationdb.NewManager(r)
	verifiedPhones, err := valMgr.GetByUsernameValidatedPhonenumbers(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	for _, validatedPhoneNumber := range verifiedPhones {
		for _, number := range userFromDB.Phonenumbers {
			if number.Phonenumber == string(validatedPhoneNumber.Phonenumber) {
				response.Sms = append(response.Sms, number)
			}
		}
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
	w.WriteHeader(http.StatusOK)
	return
}

// UpdateEmailTwoFA is the handler for GET /users/{username}/emailtwofa
// Enable or Disable Email two factor authentication
func (api UsersAPI) UpdateEmailTwoFA(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]

	emailTwoFA := struct {
		DisableEmail2FA bool `json:"disable_email_2fa"`
	}{}
	if err := json.NewDecoder(r.Body).Decode(&emailTwoFA); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	userMgr := user.NewManager(r)
	exists, err := userMgr.Exists(username)
	if !exists || err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// If disabling 2fa make sure there are other 2 factor authentication mechanisme's available
	if emailTwoFA.DisableEmail2FA {
		twoFAChecks := struct {
			Totp             bool
			hasVerifiedPhone bool
		}{}
		// get user from db
		userFromDB, err := userMgr.GetByName(username)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
			return
		}
		// check if user has otp
		totpMgr := totp.NewManager(r)
		twoFAChecks.Totp, err = totpMgr.HasTOTP(username)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		// check if user has verified phone numbers
		valMgr := validationdb.NewManager(r)
		verifiedPhones, err := valMgr.GetByUsernameValidatedPhonenumbers(username)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		for _, validatedPhoneNumber := range verifiedPhones {
			if twoFAChecks.hasVerifiedPhone {
				break
			}
			for _, number := range userFromDB.Phonenumbers {
				if number.Phonenumber == string(validatedPhoneNumber.Phonenumber) {
					twoFAChecks.hasVerifiedPhone = true
					break
				}
			}
		}
		if !twoFAChecks.Totp && !twoFAChecks.hasVerifiedPhone {
			log.Debug("User doesnt have other 2 factor authentication mechanisme's")
			http.Error(w, "User doesnt have other 2 factor authentication mechanisme's", http.StatusForbidden)
			return
		}
	}

	err = userMgr.UpdateEmailTwoFA(username, emailTwoFA.DisableEmail2FA)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusNoContent)
}

// GetTOTPSecret is the handler for GET /users/{username}/totp/
// Gets the users TOTP secret, or a new one if it doesn't exist yet
func (api UsersAPI) GetTOTPSecret(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]

	var response struct {
		Totpsecret string `json:"totpsecret"`
		TotpIssuer string `json:"totpissuer"`
	}

	totpManager := totp.NewManager(r)
	err, secret := totpManager.GetSecret(username)
	// if no existing secret is found generate a new one
	if totpManager.IsErrNotFound(err) {
		var token *totp.Token
		token, err = totp.NewToken()
		if handleServerError(w, "generating a new totp secret", err) {
			return
		}

		response.Totpsecret = token.Secret
		response.TotpIssuer = totp.GetIssuer(r)
		// an error might be an `actual` error and not just a not found
	} else if handleServerError(w, "get saved totp secret", err) {
		return
		// if there was no error then we successfully loaded an existing secret
	} else {
		response.TotpIssuer = totp.GetIssuer(r)
		response.Totpsecret = secret.Secret
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// SetupTOTP is the handler for POST /users/{username}/totp/
// Configures TOTP authentication for this user
func (api UsersAPI) SetupTOTP(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	values := struct {
		TotpSecret string `json:"totpsecret"`
		TotpCode   string `json:"totpcode"`
	}{}

	if err := json.NewDecoder(r.Body).Decode(&values); err != nil {
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	totpMgr := totp.NewManager(r)
	err := totpMgr.Save(username, values.TotpSecret)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	valid, err := totpMgr.Validate(username, values.TotpCode)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if !valid {
		err := totpMgr.Remove(username)
		if err != nil {
			log.Error(err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		w.WriteHeader(422)
	} else {
		userMgr := user.NewManager(r)
		userMgr.RemoveExpireDate(username)
		w.WriteHeader(http.StatusNoContent)
	}
}

// RemoveTOTP is the handler for DELETE /users/{username}/totp/
// Removes TOTP authentication for this user, if possible.
func (api UsersAPI) RemoveTOTP(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]

	valMngr := validationdb.NewManager(r)
	hasValidatedPhones, err := valMngr.HasValidatedPhones(username)
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	if !hasValidatedPhones {
		w.WriteHeader(http.StatusConflict)
		return
	}
	totpMgr := totp.NewManager(r)
	err = totpMgr.Remove(username)
	if err != nil && !totpMgr.IsErrNotFound(err) {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	// if the err is an error not found, there was nothing in the first place
	w.WriteHeader(http.StatusNoContent)
}

// LeaveOrganization is the handler for DELETE /users/{username}/organizations/{globalid}/leave
// Removes the user from an organization
func (api UsersAPI) LeaveOrganization(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	organizationGlobalId := mux.Vars(r)["globalid"]
	orgMgr := organizationDb.NewManager(r)
	userMgr := user.NewManager(r)
	oauthMgr := oauthservice.NewManager(r)
	// make sure the last owner can't leave an organization. only valid if this is
	// a top level organization since suborg owners are implicitly extended by the owner
	// of the parent orgs.
	if !strings.Contains(organizationGlobalId, ".") {
		org, err := orgMgr.GetByName(organizationGlobalId)
		// load the org
		if db.IsNotFound(err) {
			writeErrorResponse(w, http.StatusNotFound, "user_not_found")
			return
		}
		if handleServerError(w, "loading organization", err) {
			return
		}
		// if only one owner remains and its the user then don't let them leave
		if len(org.Owners) == 1 && org.Owners[0] == username {
			writeErrorResponse(w, http.StatusConflict, "last_owner_can't_leave")
			return
		}
	}
	err := orgMgr.RemoveUser(organizationGlobalId, username)
	if err == mgo.ErrNotFound {
		writeErrorResponse(w, http.StatusNotFound, "user_not_found")
		return
	} else if handleServerError(w, "removing user from organization", err) {
		return
	}
	err = userMgr.DeleteAuthorization(username, organizationGlobalId)
	if handleServerError(w, "removing authorization", err) {
		return
	}
	err = oauthMgr.RemoveOrganizationScopes(organizationGlobalId, username)
	if handleServerError(w, "removing organization scopes", err) {
		return
	}
	w.WriteHeader(http.StatusNoContent)
}

// ListIyoIDs is the handler for GET /users/{username}/identifiers
// Lists the iyo ids a party has generated for a user
func (api UsersAPI) ListIyoIDs(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	azp, _ := context.Get(r, "client_id").(string)

	mgr := iyoid.NewManager(r)
	idObj, err := mgr.GetByUsernameAndAZP(username, azp)
	if err != nil && !db.IsNotFound(err) {
		handleServerError(w, "listing user iyoids", err)
		return
	}
	// If nothing is found we have no iyoids yet. So just create and return a template
	// with an empty iyo ids list
	if idObj == nil {
		idObj = &iyoid.Identifier{
			Username: username,
			Azp:      azp,
			IyoIDs:   []string{},
		}
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(idObj)
}

// GenerateIyoID is the handler for POST /users/{username}/identifiers
// Generate a new iyo id for a user and authorized party
func (api UsersAPI) GenerateIyoID(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	azp, _ := context.Get(r, "client_id").(string)

	mgr := iyoid.NewManager(r)

	// might have an id collision, retry at most maxIDGenerationAttempts time
	var err error
	var id string
	for attempts := 0; attempts < maxIDGenerationAttempts; attempts++ {
		// Generate id
		id, err = tools.GenerateRandomString()
		if handleServerError(w, "generating iyoid", err) {
			return
		}
		err = mgr.UpsertIdentifier(username, azp, id)
		if err != nil && err != iyoid.ErrIDLimitReached && !db.IsDup(err) {
			handleServerError(w, "saving iyoid", err)
			return
		}
		// If the user has too many iyoids report it
		if err == iyoid.ErrIDLimitReached {
			// Max amount of iyoids reached
			log.Debugf("Max iyoid treshold reached for %s - %s", username, azp)
			http.Error(w, http.StatusText(http.StatusConflict), http.StatusConflict)
			return
		}

		// If we got ourselves a dup lets retry
		if db.IsDup(err) {
			continue
		}

		break
	}

	// If there is still an error, we have issues
	if handleServerError(w, "saving new id after multiple tries", err) {
		return
	}

	// If we manage to get here, all is good an the new id has been saved

	// Load ids again
	idObj, err := mgr.GetByUsernameAndAZP(username, azp)
	if err != nil && !db.IsNotFound(err) {
		handleServerError(w, "listing user iyo-ids", err)
		return
	}

	// Only keep the latest id generated
	idObj.IyoIDs = idObj.IyoIDs[len(idObj.IyoIDs)-1 : len(idObj.IyoIDs)]

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(idObj)
}

// LookupIyoID is the handler for GET /users/identifiers/{identifier}
// Lookup the username behind an iyo id
func (api UsersAPI) LookupIyoID(w http.ResponseWriter, r *http.Request) {
	id := mux.Vars(r)["identifier"]
	azp, _ := context.Get(r, "client_id").(string)

	mgr := iyoid.NewManager(r)
	// Get the identifier object for the id and azp
	idObj, err := mgr.GetByIDAndAZP(id, azp)
	if err != nil && !db.IsNotFound(err) {
		handleServerError(w, "listing user iyoids", err)
		return
	}

	// Report if we didn't find anything
	if db.IsNotFound(err) {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// If the identifier belongs to another azp report that we didn't find something for this user either
	if idObj.Azp != azp {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Only reveal this id in the response object
	for i, sid := range idObj.IyoIDs {
		if sid == id {
			idObj.IyoIDs = idObj.IyoIDs[i : i+1]
		}
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(idObj)
}

func writeErrorResponse(responseWrite http.ResponseWriter, httpStatusCode int, message string) {
	log.Debug(httpStatusCode, " ", message)
	errorResponse := struct {
		Error string `json:"error"`
	}{
		Error: message,
	}
	responseWrite.WriteHeader(httpStatusCode)
	json.NewEncoder(responseWrite).Encode(&errorResponse)
}

func writeValidationError(responseWrite http.ResponseWriter, httpStatusCode int, err error) {
	log.Debug(httpStatusCode, " ", err)
	errorResponse := struct {
		Error   string `json:"error"`
		Message string
	}{
		Error:   "validation_error",
		Message: fmt.Sprintf("%v", err.Error()),
	}
	responseWrite.WriteHeader(httpStatusCode)
	json.NewEncoder(responseWrite).Encode(&errorResponse)
}

func handleServerError(responseWriter http.ResponseWriter, actionText string, err error) bool {
	if err != nil {
		log.Error("Users api: Error while "+actionText, " - ", err)
		http.Error(responseWriter, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return true
	}
	return false
}

func exists(value string, list []string) bool {
	for _, val := range list {
		if val == value {
			return true
		}
	}

	return false
}

// OIDCProviderInfo represents OIDC provider information for the user
type OIDCProviderInfo struct {
	Provider  string    `json:"provider"`
	Email     string    `json:"email"`
	LastLogin time.Time `json:"lastlogin"`
}

// GetOIDCProviders is handler for GET /users/{username}/oidc-providers
func (api UsersAPI) GetOIDCProviders(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]

	userMgr := user.NewManager(r)
	usr, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Convert OIDCIdentities to OIDCProviderInfo
	var providers []OIDCProviderInfo
	for _, identity := range usr.OIDCIdentities {
		providers = append(providers, OIDCProviderInfo{
			Provider:  identity.Provider,
			Email:     identity.Email,
			LastLogin: identity.LastLogin,
		})
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(providers)
}

// RevokeOIDCProvider is handler for DELETE /users/{username}/oidc-providers/{provider}
func (api UsersAPI) RevokeOIDCProvider(w http.ResponseWriter, r *http.Request) {
	username := mux.Vars(r)["username"]
	providerID := mux.Vars(r)["provider"]

	userMgr := user.NewManager(r)
	usr, err := userMgr.GetByName(username)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Find and remove the OIDC identity
	var newIdentities []user.OIDCIdentity
	found := false
	for _, identity := range usr.OIDCIdentities {
		if identity.Provider != providerID {
			newIdentities = append(newIdentities, identity)
		} else {
			found = true
		}
	}

	if !found {
		http.Error(w, "OIDC provider not found", http.StatusNotFound)
		return
	}

	// Update the user with the new identities list
	usr.OIDCIdentities = newIdentities
	if err := userMgr.Save(usr); err != nil {
		log.Error("Error saving user after revoking OIDC provider:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}
