{"emails": "Emails", "authorize": "Authorize", "register": "Register", "login": "<PERSON><PERSON>", "window-location-host-identity-and-access-manager": "{0}  -- Identity & Access manager", "logout": "Logout", "alerter": "<PERSON><PERSON><PERSON>", "ok": "Ok", "cancel": "cancel", "confirm": "confirm", "breadcrumbs": "Breadcrumbs", "drawer": "Drawer", "notifications": "Notifications", "new-account": "New Account", "or-create-a": "Or Create a", "page": "Page", "back-to": "Back to", "send-recovery-email": "Send recovery email", "enter-your-email": "Enter your email", "email-address": "Email address", "forgot-password": "forgot password", "unknown-user": "Unknown User!", "register-now": "register now", "not-registered": "Not Registered?", "password": "Password", "username-email-or-phone-number": "Username, email or phone number", "something-went-wrong": "Something went wrong", "resend-code": "Resend code", "verification-code": "Verification code", "select-email-address-for-2fa": "Select email address for 2FA:", "select-phone-number-for-2fa": "Select phone number for 2FA:", "via-sms": "Via sms", "via-email": "Via email", "via-authenticator": "Via authenticator", "error": "Error", "passwords-dont-match": "Passwords don't match", "error-validating-email": "Error validating email", "you-already-have-an-account": "You already have an account?", "acceptable-use-policy": "Acceptable use Policy", "and": ", and", "cookie-policy": "<PERSON><PERSON>", "privacy-policy": "Privacy Policy", "terms-and-conditions": "Terms and Conditions", "i-agree-to": "I agree to", "confirm-password": "Confirm password *", "last-name": "Last name *", "first-name": "First name *", "confirm-new-password": "Confirm new password", "new-password": "New password", "reset-password": "Reset Password", "no-items-available": "No items available", "loading-items": "Loading items...", "search": "Search", "next": "Next", "previous": "Previous", "selected-items-selectedcount": "- selected items: {0}", "page-footerpagination-page-of-math-max-footerpagination-pagecount-1-total-results-footerpagination-itemslength": "Page {0} of {1}, Total results {2}", "handler-apierrorhandler-nurl-url-nreason-err-ndatapassed-json-stringify-data": "Handler: APIErrorHandler\\nURL: {0}\\nReason: {1}\\nDataPassed: {2}", "handler-promiseuncaughtrejection-nreason-event-reason": "Handler: PromiseUncaughtRejection\\nReason: {0}", "handler-window-nmessage-msg-nfile-url-nline-line-ncolumn-col-nstacktrace-error-stack": "Handler: Window\\nMessage: {0}\\nFile: {1}\\nLine: {2}\\nColumn: {3}\\nStackTrace: {4}", "handler-vue-ninfo-info-nerror-error-stack": "Handler: Vue\\nInfo: {0}\\nError' {1}", "note": "Note", "dont-show-me-this-again": "Don't show me this again", "longer-than-8-characters": "longer than 8 characters", "contains-at-least-one-special-character": "contains at least one special character", "contains-at-least-one-number": "contains at least one number", "contains-lowercase-and-uppercase-letters": "contains lowercase, and uppercase letters", "you-dont-have-any-notifications": "you don't have any notifications", "reject": "Reject", "accept": "Accept", "ctx-organization-has-invited-your-organization-ctx-user-to-become-ctx-role-owner-an-a-ctx-role": "{0} has invited your organization {1} to become {2} {3}.", "organization-invitation": "Organization invitation", "organization-ctx-organization-has-invited-you-to-become-ctx-role-owner-an-a-ctx-role": "Organization {0} has invited you to become {1} {2}.", "membership-invitation": "Membership invitation", "api-access-keys": "API access keys", "organizations": "Organizations", "update-key": "Update key", "create-key": "Create key", "may-be-used-in-client-credentials-grant-type": "May be used in client credentials grant type", "secret": "Secret", "callback-url": "Callback URL", "label": "Label", "create": "Create", "organization-name": "Organization name", "structure": "Structure", "add-suborganization": "Add suborganization", "actions": "Actions", "invite-organization": "Invite Organization", "role": "Role *", "user-this-member-is-already-member": "User {0} is already member", "people": "People", "invite-user": "Invite user", "settings": "Settings", "delete-organization": "Delete organization", "leave-organization": "Leave organization", "add-new-api-key": "Add new API key", "create-new-organization": "Create new organization", "save": "Save", "description": "Description", "status": "Status", "member-of": "Member of", "organization": "Organization", "email": "Email", "please-confirm-removing-invitation-of-ctx-user-from-organization-ctx-organization": "Please confirm removing invitation of {0} from organization {1}.", "you-sucessfully-delete-ctx-organization": "You sucessfully delete {0}", "are-you-sure-you-want-to-cancel-invitation-to-ctx-user": "Are you sure you want to cancel invitation to {0} ?", "are-you-sure-you-want-to-delete-ctx-organization": "Are you sure you want to delete {0} ?", "you-successfully-leave-ctx-organization": "You successfully leave {0}", "youre-the-last-owner-of-ctx-organization-last-owner-cant-leave": "You're the last owner of {0}, Last owner can't leave", "are-you-sure-that-you-want-to-leave-organization-ctx-organization": "Are you sure that you want to leave organization {0}", "please-confirm-to-delete-the-api-key-with-label-ctx-label-because-it-might-lead-to-service-unavailabilty-if-this-key-would-still-be-used-somewhere": "Please confirm to delete the API key with label {0} because it might lead to service unavailabilty if this key would still be used somewhere.", "api-key-secret-has-been-copied": "Api key secret has been copied!", "api-key-secret-has-been-generated": "Api key secret has been generated.", "please-confirm-to-downgrade-ctx-user-to-a-member-of-organization-ctx-organization": "Please confirm to downgrade {0} to a member of organization {1}", "please-confirm-to-upgrade-ctx-user-to-an-owner-of-organization-ctx-organization": "Please confirm to upgrade {0} to an owner of organization {1}", "you-cannot-remove-the-last-owner": "You cannot remove the last owner.", "please-confirm-removing-invitation-of-organization-ctx-user": "Please confirm removing invitation of organization {0}", "please-confirm-removing-organization-ctx-user-from-organization-ctx-organization": "Please confirm removing organization {0} from organization {1}.", "please-confirm-removing-ctx-user-from-organization-ctx-organization": "Please confirm removing {0} from organization {1}.", "invitation-pending": "Invitation pending", "something-went-wrong-please-try-again-later": "Something went wrong. Please try again later.", "an-organization-with-name-ctx-name-already-exists": "An organization with name {0} already exists.", "profile": "Profile", "spam": "Spam", "home": "Home", "work": "Work", "personal": "Personal", "verify": "Verify", "this-email-is-already-exist-please-add-another-one": "This email is already exist, please add another one.", "verify-email-address": "Verify email address", "add-email-address": "Add email address", "the-input-data-fails-during-validation": "The input data fails during validation.", "label-ctx-label-is-already-used": "Label {0} is already used.", "a-verification-code-was-sent-to-your-email-to-validate-your-ownership-fill-in-the-code-in-the-box-next-to-your-email": "A verification code was sent to your email to validate your ownership. Fill in the code in the box next to your email", "phone-numers": "Phone numers", "add-phone-number": "Add phone number", "phone-number": "Phone number", "wrong-verification-code-please-enter-the-correct-code-you-recieved-on-ctx-phonenumber": "Wrong verification code! please enter the correct code you recieved on {0}", "a-verification-code-was-sent-to-your-phone-number-to-validate-your-ownership-fill-in-the-code-in-the-box-next-to-your-phone-number": "A verification code was sent to your phone number to validate your ownership. Fill in the code in the box next to your phone number", "key": "Key", "to-check-sub-organization-membership-of": "To check sub organization membership of", "whether-you-are-an-owner-of-organization-and-allows-creating-suborganizations": "Whether you are an owner of organization, and allows creating suborganizations", "wether-you-are-a-member-of-organization": "Wether you are a member of organization", "your-public-ssh-key": "Your public ssh key", "your-validated-phone-number": "Your validated phone number", "your-phone-number": "Your phone number", "your-validated-email-address": "Your validated email address", "your-email-address": "Your email address", "your-first-and-lastname": "Your first and lastname", "your-username": "Your username", "requests-authorization-for-the-following": "requests authorization for the following:", "wrong-user-name": "Wrong user name", "these-organizations-will-become-unmanageable-if-you-delete-your-account": "These organizations will become unmanageable if you delete your account:", "you-cannot-restore-a-deleted-account-your-data-will-be-lost-forever": "You cannot restore a deleted account, your data will be lost forever.", "close": "Close", "user-name": "User Name", "enter-your-user-name": "Enter Your user name:", "all-your-personal-data-will-be-permanently-removed-from-iam": "All your personal data will be permanently removed from IAM", "all-organizations-where-your-user-is-the-only-owner-will-become-unmanageable": "All organizations where your user is the only owner will become unmanageable", "all-your-access-tokens-will-become-invalid": "All your access tokens will become invalid", "you-will-loose-all-access-given-through-iam": "You will loose all access given through IAM", "deleted-iam-accounts-cannot-be-restored": "Deleted IAM accounts cannot be restored", "what-happens-when-you-delete-your-iam-account": "What happens when you delete your IAM account?", "transfer-ownership-of-your-organizations": "Transfer ownership of your organizations", "export-all-your-personal-data": "Export all your personal data", "before-deleting-your-account": "Before deleting your account", "deleting-your-account": "Deleting your account", "organizations-membership": "Organizations membership", "ssh-keys": "SSH keys", "api-keys": "API keys", "phone-numbers": "Phone numbers", "email-addresses": "Email addresses", "you-can-export-all-your-personal-data-as-a-single-yaml-file": "You can export all your personal data as a single yaml file", "export-your-personal-data": "Export your personal data", "pending": "Pending ...", "please-confirm-to-remove-ctx-email-from-your-profile": "Please confirm to remove {0} from your profile:", "invalid-phone-number": "Invalid Phone Number", "another-email-was-sent": "Another Email was sent", "another-sms-was-sent-to-your-phone": "Another SMS was sent to your phone", "password-changed-successfully-please-login": "Password changed successfully! Please login", "email-youve-entered-is-not-used-for-a-user": "Email you've entered is not used for a user", "we-sent-you-an-email-with-instructions-on-how-to-continue-resetting-your-password": "We sent you an email with instructions on how to continue resetting your password.", "incorrect-code-please-try-again": "Incorrect code! please try again", "wrong-credentials": "Wrong Credentials!", "enter-the-code-sent-to-phone": "Enter the code sent to: {0}", "verification-code-from-application": "Verification code from application", "please-enter-your-authenticator-code": "Please enter your authenticator code", "via-text": "Via text", "cairo-cloud-authentication": "Cairo cloud - Authentication", "create-new-password": "Create new password", "we-have-updated-our-password-policy-please-update-your-password": "We have updated our password policy, Please update your password.", "change-password": "Change password", "current-password": "Current password", "your-new-password-must-be-at-least-6-digits": "your new password must be at least 6 digits", "your-current-password-is-incorrect": "Your current password is incorrect!", "your-password-successfully-changed": "Your password successfully changed!", "please-check-that-you-write-your-old-password-correctly-and-follow-new-password-instructions": "Please check that you write your old password correctly and follow new password instructions", "you-successfully-removed-item-phonenumber-from-your-profile": "You successfully removed {0} from your profile!", "please-confirm-to-remove-item-phonenumber-from-your-profile": "Please confirm to remove {0} from your profile", "please-confirm-to-remove-ctx-phone-from-your-profile": "Please confirm to remove {0} from your profile:", "and-privacy": "& Privacy", "data": "Data", "username": "Username", "export-data-as-yaml": "Export data as yaml", "delete-account": "Delete account", "add-email": "Add <PERSON>", "i-agree-to-the-terms-of-use-and-privacy-policy": "I agree to the Terms of Use and Privacy Policy.", "phone-number-verification-code": "Phone number verification code *", "email-verification-code": "Email verification code *", "cairo-cloud-register": "Cairo cloud - register", "verification-code-is-sent-to-your-email-to-validate-your-ownership-fill-in-the-code-in-the-box-below-your-email": "Verification code is sent to your email to validate your ownership. Fill in the code in the box below your email", "another-verification-email-was-sent-to-your-inbox": "Another Verification Email was sent to your inbox", "passwords-do-not-match": "Passwords do not match!", "validation-failure": "Validation failure!", "verify-email": "Verify email", "finishing-registration-failed": "Finishing registration failed.", "phone-number-validation-failed": "Phone number validation failed", "email-validation-failed": "Email validation failed.", "you-entered-a-wrong-code": "You entered a wrong code.", "your-password-is-not-strong-enough-please-try-a-stronger-password": "Your password is not strong enough. Please try a stronger password.", "ctx-phone-is-already-used-by-another-account-you-might-try-logging-in-instead": "{0} is already used by another account. You might try logging in instead?", "ctx-phone-is-not-a-valid-phone-number-please-review-or-use-a-different-phone-number-and-try-again": "{0} is not a valid phone number. Please review or use a different phone number and try again.", "ctx-email-is-already-used-by-another-account-you-might-try-logging-in-instead": "{0} is already used by another account. You might try logging in instead?", "ctx-email-is-not-a-valid-email-address-please-review-or-use-a-different-email-address-and-try-again": "{0} is not a valid email address. Please review or use a different email address and try again.", "ctx-lastname-is-not-accepted-as-a-valid-last-name-please-correct-it-and-try-again": "{0} is not accepted as a valid last name. Please correct it and try again.", "ctx-firstname-is-not-accepted-as-a-valid-first-name-please-correct-it-and-try-again": "{0}  is not accepted as a valid first name. Please correct it and try again.", "phone-number-updated-successfully-please-enter-verification-code-sent-to-the-new-phone-number": "Phone number updated successfully, Please enter verification code sent to the new phone number", "email-updated-successfully-please-enter-verification-code-sent-to-the-new-email": "Email updated successfully, Please enter verification code sent to the new email", "you-must-agree-to-the-terms-and-conditions-privacy-policy-cookie-policy-and-acceptable-use-policy": "You MUST agree to the Terms and Conditions, Privacy Policy, Cookie Policy and Acceptable use Policy", "username-updated-successfully": "Username updated successfully", "access-tokens": "Access tokens", "update": "Update", "application-id": "Application Id", "add-key": "Add key", "ssh-key": "SSH key", "setup": "Setup", "delete": "Delete", "show-existing-qr-code": "Show existing QR code", "qr-code-verification-is-enabled": "QR code verification is enabled", "authenticator-application": "Authenticator application", "add-ssh-key": "Add SSH key", "create-access-token": "Create access token", "currently-no-authenticator-application-is-configured-on-your-profile-authenticator-application-allows-you-to-receive-codes-for-your-2-step-verification-even-without-internet-connection-and-mobile-service-click-setup-button-to-configure-your-authenticator-application": "Currently no Authenticator Application is configured on your profile.  Authenticator Application allows you to receive codes for your 2-Step Verification, even without internet connection and mobile service. Click SETUP button to configure your Authenticator Application.", "please-scan-the-qr-code-with-2fa-app-and-write-the-6-digits-code-below": "please scan the QR code with 2fa app and write the 6 digits code below", "are-you-sure-you-want-to-remove-your-2fa-setup": "Are you sure you want to remove your 2fa setup ?", "your-2fa-successfully-enabled": "your 2fa successfully enabled!", "invalid-code-please-write-the-code-you-see-in-2fa-app": "invalid code! please write the code you see in 2fa app", "secret-code-copied": "Secret code copied!", "application-id-copied": "Application ID copied!", "you-are-about-to-delete-ctx-label-access-token-are-you-sure": "You are about to delete ({0}) access token. Are you sure ?", "ctx-apikeylabel-is-repeated-please-change-the-label-and-try-again": "({0}) is repeated! please change the label and try again", "ctx-apikeylabel-is-invalid-please-choose-another-access-token-label-at-least-2-characters": "({0}) is invalid! please choose another access token label \"at least 2 characters\"", "access-token-ctx-apikeylabel-successfully-added": "Access token ({0}) successfully added!", "you-are-about-to-delete-ctx-label-key-are-you-sure": "You are about to delete ({0}) key. Are you sure ?", "successfully-added": "successfully added!", "your-ssh-key-is-invalid-please-change-ssh-key-and-try-again": "your ssh key is invalid! please change ssh key and try again", "this-ssh-key-label-is-repeated-please-change-the-label-and-try-again": "this ssh key label is repeated! please change the label and try again", "your-authorizations-for-organization-this-grantedto-are-successfully-updated": "Your authorizations for organization {0} are successfully updated!", "shared-information": "Shared Information", "full-name": "full name", "information-shared-with-grantedto": "Information shared with {0}", "organizations-you-are-sharing-information-with": "Organizations you are sharing information with:", "language": "language", "profile-settings": "Profile Settings", "add-new": "Add new", "change-username": "Change username", "create-organization": "Create organization", "edit": "Edit", "invite-organization-0": "Invite organization", "add-sub-organization": "Add sub organization", "authenticator": "Authenticator", "information-shared": "Information shared", "shared-information-0": "Shared information", "control_email_2fa": "Disable two factor authentication using Email", "email_2fa_updated_successfully": "Email two factor authentication updated successfully", "or": "OR", "sign-in-with": "Sign in with {0}", "complete-profile": "Complete Profile", "add-password": "Add password", "complete-registration": "Complete Registration", "policies_not_agreed": "You must agree to all policies to proceed.", "passwords_do_not_match": "Passwords do not match!", "oidc_access_denied": "Authentication was cancelled or access was denied.", "oidc_authentication_failed": "Authentication failed. Please try again.", "oidc_error_unknown": "An authentication error occurred. Please try again.", "oidc-providers": "OIDC Providers", "provider": "Provider", "last-login": "Last Login", "no-oidc-providers": "No OIDC providers linked to your account", "revoke_oidc_provider": "Are you sure you want to revoke access to {provider}? This will remove the connection to this provider."}