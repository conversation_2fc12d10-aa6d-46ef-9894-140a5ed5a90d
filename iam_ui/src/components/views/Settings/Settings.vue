<template>
  <page :loading="loading">
    <template #content>
      <SubTopBar>
      <v-btn variant="text" v-if="showAddAccessToken" @click="toAddAccessToken" id="create_access_token">
        <v-icon>mdi-lock</v-icon>
        <span padding="10px">{{ $t('create-access-token') }}</span>
      </v-btn>
      <v-btn variant="text" v-if="showAddSshKey" @click="toAddSshKey" id="add_ssh_key">
        <v-icon>mdi-key</v-icon>
        <span padding="10px">{{ $t('add-ssh-key') }}</span>
      </v-btn>
    </SubTopBar>
    <v-breadcrumbs
      :items="bcItems"
      divider=">"
      class="mt-1"
      size="large"
      />
      <v-tabs v-model="tab" color="primary" style="min-height: 100px;">
        <v-tab :to="{name: 'AccessTokens'}"> {{ $t('access-tokens') }} </v-tab>
        <v-tab :to="{name: 'SshKey<PERSON>'}"> {{ $t('ssh-keys') }} </v-tab>
        <v-tab :to="{name: 'Authenticator'}">
          {{ $t('authenticator-application') }}
        </v-tab>
        <v-tab :to="{name: 'OIDCProviders'}"> {{ $t('oidc-providers') }} </v-tab>
      </v-tabs>
      <router-view/>
    </template>
  </page>
</template>

<script>
export default {
  data() {
    return {
      loading: true,
      tab: null
    }
  },
  computed: {
    showAddSshKey: function () {return this.$route.name == 'SshKeys'},
    showAddAccessToken: function () {return this.$route.name == 'AccessTokens'},
    bcItems: function () {
      return [
        {},
        {
          title: this.$t('settings'),
          disabled: false,
          exact: true,
          to: {name: 'Settings'}
        },
        {
          title: `${this.$route.meta.bcText}`,
          disabled: false,
          exact: true,
          href: `${this.$route.path}`
        },
      ]}
    
  },
  methods: {
    toAddSshKey() {
      this.$router.push({name: "AddSshKey"})
    },
    toAddAccessToken() {
      this.$router.push({name: "AddAccessToken"})
    }
  },
  mounted() {
    this.loading = false
  }
}
</script>


<style scoped>
div {
  padding: 10px;
}
span {
  padding: 5px 5px;
}
.v-btn {
  margin: 5px 5px;
}
</style>