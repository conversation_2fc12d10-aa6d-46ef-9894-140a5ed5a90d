import i18n from '@/plugins/i18';

export default (ctx, key) => {
  let strings = {
    /* globalstrings */
    "server_error": i18n.global.t('something-went-wrong-please-try-again-later'),
    /* SSH keys strings */
    "conflict_ssh": i18n.global.t('this-ssh-key-label-is-repeated-please-change-the-label-and-try-again'),
    "invalid_ssh_key": i18n.global.t('your-ssh-key-is-invalid-please-change-ssh-key-and-try-again'),
    "successfully_added_ssh": i18n.global.t('successfully-added'),
    "delete_ssh_key": i18n.global.t('you-are-about-to-delete-ctx-label-key-are-you-sure', [ctx.label]),
    /* Access tokens strings */
    "successfully_added_access_token": i18n.global.t('access-token-ctx-apikeylabel-successfully-added', [ctx.apiKeyLabel]),
    "invalid_access_label": i18n.global.t('ctx-apikeylabel-is-invalid-please-choose-another-access-token-label-at-least-2-characters', [ctx.apiKeyLabel]),
    "conflict_access_token": i18n.global.t('ctx-apikeylabel-is-repeated-please-change-the-label-and-try-again', [ctx.apiKeyLabel]),
    "delete_access_token": i18n.global.t('you-are-about-to-delete-ctx-label-access-token-are-you-sure', [ctx.label]),
    "successfully_updated_access_token": `Access token label successfully updated!`,
    "application_id_copied": i18n.global.t('application-id-copied'),
    "secret_copied": i18n.global.t('secret-code-copied'),
    /* Authenticator strings */
    "invalid_code": i18n.global.t('invalid-code-please-write-the-code-you-see-in-2fa-app'),
    "setup_successfully": i18n.global.t('your-2fa-successfully-enabled'),
    "confirm_delete": i18n.global.t('are-you-sure-you-want-to-remove-your-2fa-setup'),
    "setup_onclick": i18n.global.t('please-scan-the-qr-code-with-2fa-app-and-write-the-6-digits-code-below'),
    "setup_text": i18n.global.t('currently-no-authenticator-application-is-configured-on-your-profile-authenticator-application-allows-you-to-receive-codes-for-your-2-step-verification-even-without-internet-connection-and-mobile-service-click-setup-button-to-configure-your-authenticator-application'),
    /* OIDC Providers strings */
    "revoke_oidc_provider": i18n.global.t('revoke_oidc_provider', ctx)
  }
  return strings[key]
}