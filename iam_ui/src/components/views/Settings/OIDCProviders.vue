<template>
  <data-table 
    v-model="page" 
    :headers="headers"
    :items="providers"
    class="elevation-1"
    @refresh="load"
    :loading="dataLoading"
  >
    <template v-slot:[`item.lastlogin`]="{ item }">
      {{ formatDate(item.lastlogin) }}
    </template>
    <template v-slot:[`item.actions`]="{ item }">
      <v-icon color="red" size="small" @click="revokeItem(item)">
        mdi-delete
      </v-icon>
    </template>
    <template v-slot:no-data>
      <div class="text-center pa-4">
        {{ $t('no-oidc-providers') }}
      </div>
    </template>
  </data-table>
</template>

<script>
import SettingsStrings from "./SettingsStrings.js"
export default {
  data() {
    return {
      page: 1,
      dataLoading: true,
      headers: [
        { title: this.$t('provider'), align: 'start', sortable: true, value: 'provider' },
        { title: this.$t('email'), align: 'start', sortable: true, value: 'email' },
        { title: this.$t('last-login'), align: 'start', sortable: true, value: 'lastlogin' },
        { title: this.$t('actions'), value: 'actions', sortable: false },
      ],
      providers: []
    }
  },
  created: function () {
    this.load()
  },
  methods: {
    load() {
      this.$api.getOIDCProviders(this.username, this.callBack(response => {
        this.providers = response
      }, undefined, ()=> {this.dataLoading = false}))
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
    },
    revokeItem(item) {
      const provider = item.provider
      this.alert("confirm", SettingsStrings({provider: provider}, "revoke_oidc_provider"), undefined, () => {
        this.$api.revokeOIDCProvider(provider, this.username, this.callBack(
          this.load
        ))
      })
    },
  },
}
</script>

<style scoped>
.v-data-table {
  width: fit-content;
  max-width: 100%;
}
</style>
