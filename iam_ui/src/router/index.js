import { createRouter, createWebHistory} from 'vue-router'
import Login from '../components/views/Profile/Login'
import {store} from '../store'
import i18n from '../plugins/i18.js'

const getLocalizedBcText = (key) => {
  return i18n.global.t(`${key}`);
};

function setLang(route) {
  let lang = route.query.language || localStorage.getItem('language')
  // TODO: Edit the list with new languages, should also be edited in App.vue
  let availableLang = ['en', 'fr', 'es', 'nl'].includes(lang) ? lang : 'en'
  localStorage.setItem('language',  availableLang)
  i18n.locale = availableLang;
}

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      redirect: '/profile',
      meta: {}
    },
    {
      path: '/forgot-password',
      name: 'ForgotPassword',
      props: { ActivePage: 'forgotpassword' },
      component: Login,
      meta: { listed: false }
    },
    {
      path: '/resetpassword',
      name: 'NewPassword',
      props: { ActivePage: 'resetPassword' },
      component: Login,
      meta: { listed: false }
    },
    {
      path: '/register',
      name: 'Register',
      props: { ActivePage: 'register' },
      component: Login,
      meta: { listed: false }
    },
    {
      path: '/complete-oidc',
      name: 'CompleteOIDC',
      props: { ActivePage: 'oidcregister' },
      component: Login,
      meta: { listed: false }
    },
    {
      path: '/login',
      name: 'Login',
      props: { ActivePage: 'login' },
      component: Login,
      meta: { listed: false }
    },
    {
      path: '/verify-login',
      name: 'LoginVerification',
      props: { ActivePage: 'loginVerify' },
      component: Login,
      meta: { listed: false }
    },
    {
      path: '/authorize',
      name: 'Authorize',
      component: () => import('../components/views/Profile/Authorize.vue'),
      meta: { listed: false }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('../components/views/Profile/Profile.vue'),
      meta: { icon: 'mdi-account', listed: true, bcText: getLocalizedBcText('profile') },
      redirect: '/profile/emails',
      children: [
        {
          path: 'emails',
          name: 'Emails',
          component: () => import('../components/views/Profile/Emails.vue'),
          meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('emails') }
        },
        {
          path: 'phonenumbers',
          name: 'PhoneNumbers',
          component: () => import('../components/views/Profile/PhoneNumbers.vue'),
          meta: { listed: false, bcText: getLocalizedBcText('phone-numbers') }
        },
        {
          path: 'privacy',
          name: 'DataAndPrivacy',
          component: () => import('../components/views/Profile/DataAndPrivacy.vue'),
          meta: { listed: false, bcText: getLocalizedBcText('profile-settings') }
        }
      ]
    },
    {
      path: '/profile/emails/add',
      name: 'AddEmail',
      component: () => import('../components/views/Profile/AddEmail.vue'),
      meta: { listed: false, bcText: getLocalizedBcText('add-new') }
    },
    {
      path: '/profile/emails/:label/verify',
      name: 'VerifyEmail',
      component: () => import('../components/views/Profile/AddEmail.vue'),
      meta: { listed: false, bcText: getLocalizedBcText('verify') }
    },
    {
      path: '/profile/phonepumbers/add',
      name: 'AddPhoneNumber',
      component: () => import('../components/views/Profile/AddPhoneNumber.vue'),
      meta: { listed: false, bcText: getLocalizedBcText('add-new') }
    },{
      path: '/profile/phonenumbers/:label/verify',
      name: 'VerifyPhoneNumber',
      component: () => import('../components/views/Profile/AddPhoneNumber.vue'),
      meta: { listed: false, bcText: getLocalizedBcText('verify') }
    },
    {
      path: '/profile/password/change',
      name: 'PasswordChange',
      component: () => import('../components/views/Profile/PasswordChange.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('change-password') }
    },
    {
      path: '/profile/username/change',
      name: 'UsernameChange',
      component: () => import('../components/views/Profile/UsernameChange.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('change-username') }
    },
    {
      path: '/organizations',
      name: 'Organizations',
      component: () => import('../components/views/Organizations/Organizations.vue'),
      meta: { icon: 'mdi-account-group', listed: true, bcText: getLocalizedBcText('organizations') }
    },
    {
      path: '/organizations/add',
      name: 'AddOrganization',
      component: () => import('../components/views/Organizations/AddOrganization.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('create-organization') }
    },
    {
      path: '/organizations/:organization/',
      name: 'OrganizationManagement',
      component: () => import('../components/views/Organizations/OrganizationManagement.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('organizations') },
      children: [
        {
          path: '/organizations/:organization/people',
          name: 'OrganizationPeople',
          component: () => import('../components/views/Organizations/People.vue'),
          meta: { listed: false, bcText: getLocalizedBcText('people') }
        },
        {
          path: '/organizations/:organization/organizations',
          name: 'OrganizationOrganizations',
          component: () => import('../components/views/Organizations/OrgOrganizations.vue'),
          meta: { listed: false, bcText: getLocalizedBcText('organizations') }
        },
        {
          path: '/organizations/:organization/structure',
          name: 'OrganizationStructure',
          component: () => import('../components/views/Organizations/Structure.vue'),
          meta: { listed: false, bcText: getLocalizedBcText('structure') }
        },
        {
          path: '/organizations/:organization/api-access-keys',
          name: 'OrganizationApiAccessKeys',
          component: () => import('../components/views/Organizations/ApiAccessKeys.vue'),
          meta: { listed: false, bcText: getLocalizedBcText('api-access-keys') }
        },
        {
          path: '/organizations/:organization/settings',
          name: 'OrganizationSettings',
          component: () => import('../components/views/Organizations/OrganizationSettings.vue'),
          meta: { listed: false, bcText: getLocalizedBcText('settings') }
        }
      ]
    },
    {
      path: '/organizations/:organization/api-access-keys/add',
      name: 'OrganizationAddAPIKey',
      component: () => import('../components/views/Organizations/AddKey.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('add-new') }
    },
    {
      path: '/organizations/:organization/api-access-keys/:label/edit',
      name: 'OrganizationEditAPIKey',
      component: () => import('../components/views/Organizations/AddKey.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('edit') }
    },
    {
      path: '/organizations/:organization/organizations/invite',
      name: 'InviteOrg',
      component: () => import('../components/views/Organizations/InviteOrg.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('invite-organization-0') }
    },
    {
      path: '/organizations/:organization/people/invite',
      name: 'InviteUser',
      component: () => import('../components/views/Organizations/InviteUser.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('invite-user') }
    },
    {
      path: '/organizations/:organization/organizations/add',
      name: 'AddSubOrganization',
      component: () => import('../components/views/Organizations/AddSubOrg.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('add-sub-organization') }
    },
    {
      path: '/settings',
      name: 'Settings',
      component: () => import('../components/views/Settings/Settings.vue'),
      meta: { icon: 'mdi-cog', listed: true, bcText: getLocalizedBcText('settings') },
      redirect: '/settings/access-tokens',
      children: [
        {
          path: 'access-tokens',
          name: 'AccessTokens',
          component: () => import('../components/views/Settings/AccessTokens.vue'),
          meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('access-tokens') }
        },
        {
          path: 'ssh-keys',
          name: 'SshKeys',
          component: () => import('../components/views/Settings/SshKeys.vue'),
          meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('ssh-keys') }
        },
        {
          path: 'authenticator',
          name: 'Authenticator',
          component: () => import('../components/views/Settings/Authenticator.vue'),
          meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('authenticator') }
        },
        {
          path: 'oidc-providers',
          name: 'OIDCProviders',
          component: () => import('../components/views/Settings/OIDCProviders.vue'),
          meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('oidc-providers') }
        }
      ]
    },
    {
      path: '/settings/ssh-keys/add',
      name: 'AddSshKey',
      component: () => import('../components/views/Settings/AddSshKey.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('add-new') }
    },
    {
      path: '/settings/ssh-keys/:publicKey',
      name: 'EditSshKey',
      component: () => import('../components/views/Settings/AddSshKey.vue'),
      meta: { listed: false, bcText: getLocalizedBcText('edit') }
    },
    {
      path: '/settings/access-tokens/add',
      name: 'AddAccessToken',
      component: () => import('../components/views/Settings/AddAccessToken.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('add-new') }
    },
    {
      path: '/settings/access-tokens/:ApiKey',
      name: 'EditAccessToken',
      component: () => import('../components/views/Settings/AddAccessToken.vue'),
      meta: { icon: 'mdi-account', listed: false, bcText: getLocalizedBcText('edit') }
    },
    {
      path: '/:globalId/info-shared',
      name: 'InfoShared',
      component: () => import('../components/views/SharedInfo/InfoShared.vue'),
      meta: { icon: 'mdi-repeat-variant', listed: false, bcText: getLocalizedBcText('information-shared') }
    },
    {
      path: '/shared-info',
      name: 'SharedInfo',
      component: () => import('../components/views/SharedInfo/SharedInfo.vue'),
      meta: { icon: 'mdi-repeat-variant', listed: true, bcText: getLocalizedBcText('shared-information-0') }
    },
    {
      path: '/notifications',
      name: 'Notifications',
      component: () => import('../components/views/Notifications/Notifications.vue'),
      meta: { icon: 'mdi-flag', listed: false, bcText: getLocalizedBcText('notifications') }
    }
  ]

})
router.beforeResolve( (to, from, next) => {
  setLang(to)
  to.meta.routeParams = {...to.params}
  to.params = {...from.meta.routeParams, ...to.params}
  if (store.state.user == "" && to.path == '/profile/emails') {
    next({ name: 'Login' })
  } else next()
})

export default router
